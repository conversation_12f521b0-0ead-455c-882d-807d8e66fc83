import mongoose from "mongoose";

const jp_gameSchema = new mongoose.Schema({
  game_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  image: {
    type: String,
    required: true,
  },
  blockchain: {
    type: String,
    required: true,
  },
  category: {
    type: String,
    required: true,
  },
  min_reward: {
    type: Number,
    required: true,
  },
  max_reward: {
    type: Number,
    required: true,
  },
  difficulty: {
    type: String,
    enum: ["Easy", "Medium", "Hard"],
    required: true,
  },
  featured: {
    type: String,
    required: true,
    default: true,
  },
  trending: {
    type: Boolean,
    required: true,
    default: true,
  },
});

export const JP_Game = mongoose.model("JP_Game", jp_gameSchema);
