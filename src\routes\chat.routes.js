import express from "express";
import { validateBody } from "../middlewares/validateBody.js";
// import { } from "../schemaValidation/chat.schema.js";
import { readMessages, sendMessage } from "../controllers/chat.controllers.js";
import { userAuth } from "../middlewares/authentication.js";

const router = express.Router();

router.post("/send-message", userAuth, sendMessage);
router.patch("/read-messages", userAuth, readMessages);

export default router;
