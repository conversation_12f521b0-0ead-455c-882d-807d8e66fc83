// MongoDb Models
import { Earn } from "../models/Earn.js";
import { User } from "../models/User.js";

// utils
import { generateController } from "../utils/generateController.js";
// import { getNftData } from "../utils/getNftData.js";

//
// import { ObjectId } from "mongodb";
import moment from "moment";
import { dailyRewardData } from "../data/dailyReward.js";

const updateDailyReward = generateController(
  async (request, response, raiseException) => {
    const { userId } = request.user;

    const earnData = await Earn.findOne({ user_id: userId });

    if (!earnData) {
      return raiseException(
        500,
        "An error occurred while updating daily reward"
      );
    }

    const dailyReward = JSON.parse(JSON.stringify(earnData.daily_reward));

    const lastBuyTime = moment(dailyReward.time);
    const todayTime = moment();

    // Calculate the difference in days
    const differenceInDays = todayTime.diff(lastBuyTime, "days");

    if (differenceInDays < 1) {
      return raiseException(406, "Unable to update");
    }

    earnData.daily_reward = {
      day:
        differenceInDays === 1 && dailyReward.day < 10
          ? dailyReward.day + 1
          : 1,
      time: todayTime.toISOString(),
    };
    const result = await earnData.save();

    if (!result) {
      return raiseException(
        500,
        "An error occurred while updating daily reward"
      );
    }

    const pointsToUpdate =
      dailyRewardData[
        differenceInDays === 1 && dailyReward.day < 10 ? dailyReward.day : 0
      ];

    const userData = await User.findOneAndUpdate(
      { _id: userId, is_deleted: false },
      {
        $inc: {
          total_points: pointsToUpdate,
        },
      },
      { new: true }
    );

    if (!userData) {
      return raiseException(
        500,
        "An error occurred while redeeming daily reward"
      );
    }

    return {
      message: "Daily reward redeemed",
      payload: { points_to_update: pointsToUpdate },
    };
  }
);

const checkSocialReward = generateController(
  async (request, response, raiseException) => {
    const { social_name } = request.body;
    const { userId } = request.user;

    const earnData = await Earn.findOne({ user_id: userId });

    if (!earnData) {
      return raiseException(
        500,
        `An error occurred while checking ${social_name}'s reward`
      );
    }

    const { [social_name]: socialReward } = earnData;

    if (socialReward.value) {
      return raiseException(406, "Unable to check");
    }

    const todayTime = moment();

    let successRespMessage;

    if (socialReward.check_time) {
      const lastCheckTime = moment(socialReward.check_time);

      // Calculate the difference in hours
      const differenceInHours = todayTime.diff(lastCheckTime, "hours");

      if (differenceInHours < 1) {
        return raiseException(406, "Unable to check");
      }

      socialReward.value = true;
      successRespMessage = "Social check successful";
    } else {
      socialReward.check_time = todayTime.toISOString();
      successRespMessage = "Check in progress";
    }

    const result = await earnData.save();

    if (!result) {
      return raiseException(
        500,
        `An error occurred while updating ${social_name}'s reward`
      );
    }

    let pointsToUpdate = 0;

    if (socialReward.value) {
      pointsToUpdate = 5000;
      const userData = await User.findOneAndUpdate(
        { _id: userId, is_deleted: false },
        {
          $inc: {
            total_points: pointsToUpdate,
          },
        },
        { new: true }
      );

      if (!userData) {
        return raiseException(500, "An error occurred while updating points");
      }
    }

    return {
      message: successRespMessage,
      payload: {
        points_to_update: pointsToUpdate,
        [social_name]: socialReward,
      },
    };
  }
);

const checkExchangeSelect = generateController(
  async (request, response, raiseException) => {
    const { userId } = request.user;

    const earnData = await Earn.findOne({ user_id: userId });

    if (!earnData) {
      return raiseException(500, "An error occurred while checking exchange");
    }

    if (earnData.exchange_select) {
      return raiseException(406, "Unable to check");
    }

    const userData = await User.findOne({ _id: userId, is_deleted: false });

    if (!userData) {
      return raiseException(500, "An error occurred while checking exchange");
    }

    if (!userData.exchange) {
      return raiseException(406, "Please select exchange");
    }

    earnData.exchange_select = true;
    const result = await earnData.save();

    if (!result) {
      return raiseException(500, "An error occurred while checking exchange");
    }

    const pointsToUpdate = 5000;
    userData.total_points += pointsToUpdate;
    const userResult = await userData.save();

    if (!userResult) {
      return raiseException(500, "An error occurred while updating points");
    }

    return {
      message: "Exchange check successful",
      payload: { points_to_update: pointsToUpdate },
    };
  }
);

const checkCountrySelect = generateController(
  async (request, response, raiseException) => {
    const { userId } = request.user;

    const earnData = await Earn.findOne({ user_id: userId });

    if (!earnData) {
      return raiseException(500, "An error occurred while checking exchange");
    }

    if (earnData.country_select) {
      return raiseException(406, "Unable to check");
    }

    const userData = await User.findOne({ _id: userId, is_deleted: false });

    if (!userData) {
      return raiseException(500, "An error occurred while checking exchange");
    }

    if (!userData.country) {
      return raiseException(406, "Please select exchange");
    }

    earnData.country_select = true;
    const result = await earnData.save();

    if (!result) {
      return raiseException(500, "An error occurred while checking exchange");
    }

    const pointsToUpdate = 5000;
    userData.total_points += pointsToUpdate;
    const userResult = await userData.save();

    if (!userResult) {
      return raiseException(500, "An error occurred while updating points");
    }

    return {
      message: "Country check successful",
      payload: { points_to_update: pointsToUpdate },
    };
  }
);

const checkInvFriends = generateController(
  async (request, response, raiseException) => {
    const { userId } = request.user;

    const earnData = await Earn.findOne({ user_id: userId });

    if (!earnData) {
      return raiseException(
        500,
        "An error occurred while checking invited friends"
      );
    }

    if (earnData.invite_three_friends) {
      return raiseException(406, "Unable to check");
    }

    const userData = await User.findOne({ _id: userId, is_deleted: false });

    if (!userData) {
      return raiseException(
        500,
        "An error occurred while checking invited friends"
      );
    }

    if (userData.referrals.length < 3) {
      return raiseException(406, "Unable to update");
    }

    earnData.invite_three_friends = true;
    const result = await earnData.save();

    if (!result) {
      return raiseException(
        500,
        "An error occurred while checking invited friends"
      );
    }

    const pointsToUpdate = 25000;
    userData.total_points += pointsToUpdate;
    const userResult = await userData.save();

    if (!userResult) {
      return raiseException(500, "An error occurred while updating points");
    }

    return {
      message: "Invited friends check successful",
      payload: { points_to_update: pointsToUpdate },
    };
  }
);

export {
  updateDailyReward,
  checkSocialReward,
  checkExchangeSelect,
  checkCountrySelect,
  checkInvFriends,
};
