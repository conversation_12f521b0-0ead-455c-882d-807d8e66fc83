import express from "express";
import { validateBody } from "../middlewares/validateBody.js";
import { checkSocialRewardSchema } from "../schemaValidation/earn.schema.js";
import {
  updateDailyReward,
  checkSocialReward,
  checkExchangeSelect,
  checkCountrySelect,
  checkInvFriends,
} from "../controllers/earn.controllers.js";
import { userAuth } from "../middlewares/authentication.js";

const router = express.Router();

router.get("/daily-reward", userAuth, updateDailyReward);
router.post(
  "/check/social-reward",
  userAuth,
  validateBody(checkSocialRewardSchema),
  checkSocialReward
);
router.get("/check/exchange-select", userAuth, checkExchangeSelect);
router.get("/check/country-select", userAuth, checkCountrySelect);
router.get("/check/invited-friends", userAuth, checkInvFriends);

export default router;
