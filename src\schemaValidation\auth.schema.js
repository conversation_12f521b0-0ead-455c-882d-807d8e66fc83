import joi from "joi";

const idSchema = joi
  .number()
  .integer()
  .min(0) // Ensure the ID is non-negative
  .max(Number.MAX_SAFE_INTEGER); // Ensure it doesn't exceed the max safe integer value in JavaScript

const loginSchema = joi.object().keys({
  tg_id: idSchema.required(),
});

const signupSchema = joi.object().keys({
  ref_id: idSchema,
  tg_id: idSchema.required(),
  is_bot: joi.boolean(),
  first_name: joi.string().required(),
  last_name: joi.string(),
  username: joi.string(),
  is_premium: joi.boolean(),
});

const adminLoginSchema = joi.object().keys({
  email: joi
    .string()
    .required()
    .email({ tlds: { allow: false } }),
  password: joi
    .string()
    .min(8)
    .regex(/(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*\W)/)
    .messages({
      "string.pattern.base":
        "Your password must be at least 8 characters and contain uppercase and lowercase letters, numbers, and a special character.",
    }),
});

export { loginSchema, signupSchema, adminLoginSchema };
