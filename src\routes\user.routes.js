import express from "express";
import { validateBody } from "../middlewares/validateBody.js";
import { userAuth, api<PERSON>ey<PERSON><PERSON> } from "../middlewares/authentication.js";
import {
  updatePointsSchema,
  updateExchangeSchema,
  updateCountrySchema,
  cardPurchaseSchema,
  affiliateProductInteractionSchema,
  boosterPurchaseSchema,
  changeSettingsSchema,
  closeAppSchema,
  continueAppSchema,
  updateInGameNameSchema,
  updateWalletAddressSchema,
  socialCardInteractionSchema,
  socialPointsPurchaseSchema,
} from "../schemaValidation/user.schema.js";
import {
  getGameData,
  getWebData,
  updatePoints,
  updateWalletAddress,
  updateInGameName,
  updateExchange,
  updateCountry,
  cardPurchase,
  SocialPointsPurchase,
  SocialPointsScratcher,
  affiliateProductInteraction,
  socialCardInteraction,
  boosterPurchase,
  getTopRankedUsers,
  getInvitedFriends,
  changeSettings,
  continueApp,
  closeApp,
  giveJqToReferral,
  retrieveJqMilestone,
  convertSocialPoints,
  claimJqPoints,
  jqPointsClaimed,
  FreezeSocialPoints,
  UnfreezeSocialPoints,
  // giveJqToReferrals,
  // updateBoosters,
} from "../controllers/user.controllers.js";

const router = express.Router();

router.get("/game-data", userAuth, getGameData);
router.get("/web-data", apiKeyAuth, getWebData);
router.patch(
  "/points",
  userAuth,
  validateBody(updatePointsSchema),
  updatePoints
);
router.patch(
  "/wallet-address",
  userAuth,
  validateBody(updateWalletAddressSchema),
  updateWalletAddress
);
router.patch(
  "/in-game-name",
  userAuth,
  validateBody(updateInGameNameSchema),
  updateInGameName
);
router.patch(
  "/exchange",
  userAuth,
  validateBody(updateExchangeSchema),
  updateExchange
);
router.patch(
  "/country",
  userAuth,
  validateBody(updateCountrySchema),
  updateCountry
);
router.patch(
  "/card-purchase",
  userAuth,
  validateBody(cardPurchaseSchema),
  cardPurchase
);
router.patch(
  "/social-points-purchase",
  userAuth,
  validateBody(socialPointsPurchaseSchema),
  SocialPointsPurchase
);
router.patch(
  "/social-points-scratcher",
  apiKeyAuth,
  SocialPointsScratcher
);
router.patch(
  "/social-points/freeze",
  apiKeyAuth,
  FreezeSocialPoints
);
router.patch(
  "/social-points/unfreeze",
  apiKeyAuth,
  UnfreezeSocialPoints
);
router.patch(
  "/affiliate-product-interaction",
  userAuth,
  validateBody(affiliateProductInteractionSchema),
  affiliateProductInteraction
);
router.patch(
  "/social-card-interaction",
  userAuth,
  validateBody(socialCardInteractionSchema),
  socialCardInteraction
);
router.patch(
  "/booster-purchase",
  userAuth,
  validateBody(boosterPurchaseSchema),
  boosterPurchase
);
router.get("/top-rank", userAuth, getTopRankedUsers);
router.get("/invited-friends", userAuth, getInvitedFriends);
router.patch(
  "/settings",
  userAuth,
  validateBody(changeSettingsSchema),
  changeSettings
);
router.patch("/close-app", userAuth, validateBody(closeAppSchema), closeApp);
router.patch(
  "/continue-app",
  userAuth,
  validateBody(continueAppSchema),
  continueApp
);
router.patch("/referral/give-jq", userAuth, giveJqToReferral);
router.patch("/retrieve-jq-milestone", userAuth, retrieveJqMilestone);
router.get("/jq-points/claim", userAuth, claimJqPoints);
router.patch("/jq-points/claimed", userAuth, jqPointsClaimed);
router.patch("/social-points/convert", userAuth, convertSocialPoints);
// router.get("/referrals/give-jq", giveJqToReferrals);
// router.get("/", updateBoosters);

export default router;
