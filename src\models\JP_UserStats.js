import mongoose from "mongoose";

const jp_userStatsSchema = new mongoose.Schema({
  user_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  game_id: {
    type: String,
    required: true,
  },
  best_score: {
    type: Number,
    required: true,
    default: 0,
  },
  games_played: {
    type: Number,
    required: true,
    default: 0,
  },
  total_score: {
    type: Number,
    required: true,
    default: 0,
  },
});

export const JP_UserStats = mongoose.model("JP_UserStats", jp_userStatsSchema);
