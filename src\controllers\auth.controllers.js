// MongoDb Models
import { User } from "../models/User.js";
import { Earn } from "../models/Earn.js";
import { Admin } from "../models/Admin.js";
import { Token } from "../models/Token.js";
import { Booster } from "../models/Booster.js";
import { Card } from "../models/Card.js";

// utils
import { generateController } from "../utils/generateController.js";
import { generateAccessToken } from "../utils/generateAccessToken.js";
// import { getNftData } from "../utils/getNftData.js";

//
import { ObjectId } from "mongodb";
import moment from "moment";
import bcrypt from "bcrypt";

const login = generateController(async (request, response, raiseException) => {
  const { tg_id } = request.body;

  const userData = await User.aggregate([
    {
      $match: { tg_id, is_deleted: false },
    },
    {
      $lookup: {
        from: User.collection.name,
        let: { user_tg_id: "$tg_id" },
        pipeline: [
          {
            $match: {
              $expr: {
                $in: ["$$user_tg_id", "$referrals.tg_id"],
              },
            },
          },
          {
            $project: {
              in_game_name: {
                $ifNull: [
                  "$in_game_name",
                  {
                    $cond: {
                      if: { $eq: [{ $type: "$last_name" }, "missing"] },
                      then: "$first_name",
                      else: { $concat: ["$first_name", " ", "$last_name"] },
                    },
                  },
                ],
              },
              retrieved_jq_from_ref: {
                $arrayElemAt: [
                  {
                    $filter: {
                      input: "$referrals",
                      as: "referral",
                      cond: { $eq: ["$$referral.tg_id", "$$user_tg_id"] }, // Filter to include only the matching referral
                    },
                  },
                  0,
                ],
              },
              availability_status: 1, // Keep original status field
              is_deleted: 1, // Include to check for deactivation
              tg_id: 1,
              total_points: 1,
            },
          },
          {
            $project: {
              tg_id: 1,
              in_game_name: 1,
              total_points: 1,
              retrieved_jq_from_ref:
                "$retrieved_jq_from_ref.retrieved_jq_from_ref", // Extract the boolean value
              availability_status: {
                $cond: {
                  if: {
                    $and: [
                      { $eq: ["$availability_status", "offline"] },
                      "$is_deleted",
                    ],
                  },
                  then: "deactivated",
                  else: "$availability_status",
                },
              },
            },
          },
        ],
        as: "referred_by",
      },
    },
    {
      $unwind: {
        path: "$referred_by",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $project: {
        total_points: 0,
        wallet_address: 0,
        in_game_name: 0,
        exchange: 0,
        country: 0,
        // cards_levels: 0,
        cards_data: 0,
        haptic_feedback: 0,
      },
    },
  ]).exec();

  if (!(userData && userData.length > 0)) {
    return raiseException(404, "User not found");
  }

  const token = generateAccessToken({
    userId: userData[0]._id,
  });

  const isToken = await Token.create({ user_id: userData[0]._id, token });

  if (!isToken) {
    return raiseException(500, "Token creation failed");
  }

  return {
    message: "User logged in successfully",
    payload: {
      user: userData[0],
      token,
    },
  };
});

const loginWithAuth = generateController(
  async (request, response, raiseException) => {
    const { userId, oldToken } = request.user;

    const userData = await User.aggregate([
      {
        $match: { _id: new ObjectId(userId), is_deleted: false },
      },
      {
        $lookup: {
          from: User.collection.name,
          let: { user_tg_id: "$tg_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: ["$$user_tg_id", "$referrals.tg_id"],
                },
              },
            },
            {
              $project: {
                in_game_name: {
                  $ifNull: [
                    "$in_game_name",
                    {
                      $cond: {
                        if: { $eq: [{ $type: "$last_name" }, "missing"] },
                        then: "$first_name",
                        else: { $concat: ["$first_name", " ", "$last_name"] },
                      },
                    },
                  ],
                },
                retrieved_jq_from_ref: {
                  $arrayElemAt: [
                    {
                      $filter: {
                        input: "$referrals",
                        as: "referral",
                        cond: { $eq: ["$$referral.tg_id", "$$user_tg_id"] }, // Filter to include only the matching referral
                      },
                    },
                    0,
                  ],
                },
                availability_status: 1, // Keep original status field
                is_deleted: 1, // Include to check for deactivation
                tg_id: 1,
                total_points: 1,
              },
            },
            {
              $project: {
                tg_id: 1,
                in_game_name: 1,
                total_points: 1,
                retrieved_jq_from_ref:
                  "$retrieved_jq_from_ref.retrieved_jq_from_ref", // Extract the boolean value
                availability_status: {
                  $cond: {
                    if: {
                      $and: [
                        { $eq: ["$availability_status", "offline"] },
                        "$is_deleted",
                      ],
                    },
                    then: "deactivated",
                    else: "$availability_status",
                  },
                },
              },
            },
          ],
          as: "referred_by",
        },
      },
      {
        $unwind: {
          path: "$referred_by",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          total_points: 0,
          wallet_address: 0,
          in_game_name: 0,
          exchange: 0,
          country: 0,
          // cards_levels: 0,
          cards_data: 0,
          haptic_feedback: 0,
        },
      },
    ]).exec();

    if (!(userData && userData.length > 0)) {
      return raiseException(404, "User doesn't exist");
    }

    const token = generateAccessToken({
      userId: userData[0]._id,
    });

    const isToken = await Token.updateOne(
      { user_id: userData[0]._id, token: oldToken },
      { $set: { token } }
    ).exec();

    if (!isToken) {
      return raiseException(500, "Token creation failed");
    }

    return {
      message: "User logged in successfully",
      payload: {
        user: userData[0],
        token,
      },
    };
  }
);

const signup = generateController(async (request, response, raiseException) => {
  const { ref_id, is_bot, ...data } = request.body;

  if (is_bot) {
    return raiseException(406, "Bot is not allowed to sign-up");
  }

  const existingUser = await User.findOne({ tg_id: data.tg_id });

  if (existingUser && !existingUser.is_deleted) {
    return raiseException(500, "User already exists");
  }

  if (existingUser) {
    const isDeleted = await User.findByIdAndDelete(existingUser._id);

    if (!isDeleted) {
      return raiseException(500, "Sign-up unsuccessful");
    }
  }

  let refUserData;

  if (ref_id && !existingUser) {
    refUserData = await User.findOneAndUpdate(
      { tg_id: ref_id, is_deleted: false },
      {
        $push: { referrals: { tg_id: data.tg_id } },
        $inc: { total_points: !data.is_premium ? 5000 : 25000 },
      },
      { new: true }
    ).select({
      tg_id: 1,
      first_name: 1,
      last_name: 1,
      in_game_name: 1,
      referrals: { $elemMatch: { tg_id: data.tg_id } },
      cards_data: 1,
    });
  }

  if (existingUser) {
    refUserData = await User.findOne(
      { "referrals.tg_id": data.tg_id, is_deleted: false },
      {
        tg_id: 1,
        first_name: 1,
        last_name: 1,
        in_game_name: 1,
        referrals: { $elemMatch: { tg_id: userData.tg_id } },
        cards_data: 1,
      }
    );
  }
  const totalUsers = await User.countDocuments();

  const userData = await User.create({
    ...data,
    total_points: refUserData ? (!data.is_premium ? 5000 : 25000) : 0,
    // P10
    jargon_quest: totalUsers < 100000 ? 100 : 0,
  });

  if (!userData) {
    return raiseException(500, "Sign-up unsuccessful");
  }

  const token = generateAccessToken({
    userId: userData._id,
  });

  const isToken = await Token.create({ user_id: userData._id, token });

  if (!isToken) {
    return raiseException(500, "Token creation failed");
  }

  const earnData = await Earn.create({
    user_id: userData._id,
    daily_reward: {
      time: moment().subtract(1, "days").toISOString(),
    },
  });

  if (!earnData) {
    return raiseException(500, "An error occurred while signing up");
  }

  const boosterData = await Booster.create({
    user_id: userData._id,
    energy_time: moment().toISOString(),
  });

  if (!boosterData) {
    return raiseException(500, "An error occurred while signing up");
  }

  const keysToExclude = [
    "total_points",
    "wallet_address",
    "in_game_name",
    "exchange",
    "country",
    // "cards_levels",
    "cards_data",
    "haptic_feedback",
  ];

  const userDataToSend = Object.fromEntries(
    Object.entries(userData._doc).filter(
      ([key]) => !keysToExclude.includes(key)
    )
  );

  if (refUserData) {
    const {
      tg_id,
      first_name,
      last_name,
      in_game_name,
      referrals,
      cards_data,
    } = refUserData;
    userDataToSend.referred_by = {
      in_game_name: in_game_name || `${first_name} ${last_name || ""}`.trim(),
      retrieved_jq_from_ref: referrals?.[0]?.retrieved_jq_from_ref,
    };

    const referredByx2 = await User.findOne(
      { "referrals.tg_id": tg_id, is_deleted: false },
      {
        jargon_quest: 1,
        referrals: 1,
      }
    );

    if (referredByx2) {
      const mineCards = await Card.find(
        {},
        { base_pph: 1, profit_increase_rate: 1 }
      ).exec();

      if (!mineCards) {
        return raiseException(500, "An error occurred while signing up");
      }

      let value = 0;

      if (mineCards && cards_data) {
        for (let cardId of Object.keys(cards_data)) {
          const card = mineCards.find((card) => card._id === cardId);
          if (card != undefined) {
            value +=
              card.base_pph *
              ((card.profit_increase_rate ** cards_data[cardId].level - 1) /
                (card.profit_increase_rate - 1));
          }
        }
      }

      if (value >= 10000) {
        referredByx2.jargon_quest += 50;
        referredByx2.referrals[0].retrieved_jq_from_ref = true;
        const result = await referredByx2.save();

        if (!result) {
          return raiseException(500, "An error occurred while signing up");
        }
      }
    }
  }

  return {
    message: "User signed up successfully",
    payload: {
      user: userDataToSend,
      token,
    },
  };
});

const deleteUser = generateController(
  async (request, response, raiseException) => {
    const { userId } = request.user;

    const userData = await User.findOne({ _id: userId, is_deleted: false });

    if (!userData) {
      return raiseException(500, "An error occurred while deleting user");
    }

    const tokensData = await Token.deleteMany({ user_id: userId });

    if (!(tokensData.deletedCount > 0)) {
      return raiseException(500, "An error occurred while deleting user");
    }

    const earnData = await Earn.deleteOne({ user_id: userId });

    if (!(earnData.deletedCount > 0)) {
      return raiseException(500, "An error occurred while deleting user");
    }

    const boosterData = await Booster.deleteOne({ user_id: userId });

    if (!(boosterData.deletedCount > 0)) {
      return raiseException(500, "An error occurred while deleting user");
    }

    userData.is_deleted = true;

    const result = await userData.save();

    if (!result) {
      return raiseException(500, "An error occurred while deleting user");
    }

    return {
      message: "User deleted successfully",
    };
  }
);

const adminLogin = generateController(
  async (request, response, raiseException) => {
    const { email, password } = request.body;

    const admin = await Admin.findOne({ email }).exec();

    if (!admin) {
      return raiseException(404, "Admin with this email doesn't exist");
    }

    bcrypt.compare(password, admin.password, async (err, result) => {
      if (err) {
        return raiseException(500, "Auth failed");
      }

      if (!result) {
        return raiseException(403, "Invalid Password");
      }

      const token = generateAccessToken({
        userId: admin._id,
        isAdmin: true,
      });

      try {
        const isToken = await Token.create({ user_id: admin._id, token });

        if (!isToken) {
          return raiseException(500, "Token creation failed");
        }

        response.status(200).json({
          message: "Admin logged in successfully",
          payload: { token },
          success: true,
        });
      } catch (err) {
        return raiseException(
          500,
          err.message || "An error occurred during login"
        );
      }
    });
  }
);

const adminLoginWithAuth = generateController(
  async (request, response, raiseException) => {
    const { userId, oldToken } = request.user;

    const token = generateAccessToken({
      userId,
      isAdmin: true,
    });

    const isToken = await Token.updateOne(
      { user_id: userId, token: oldToken },
      { $set: { token } }
    ).exec();

    if (!isToken) {
      return raiseException(500, "Token creation failed");
    }

    return {
      message: "Admin logged in successfully",
      payload: {
        token,
      },
    };
  }
);

export {
  login,
  loginWithAuth,
  signup,
  deleteUser,
  adminLogin,
  adminLoginWithAuth,
};
