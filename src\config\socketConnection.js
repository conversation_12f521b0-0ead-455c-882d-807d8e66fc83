import { Server as ServerIO } from "socket.io";
import { addBonusOnClose } from "../utils/bonusAdder.js";

//
// import SocketController from '../handlers/socketController';

const connections = [];
const webSocketConnection = (server) => {
  const options = {
    // pingTimeout: 3000,
    // pingInterval: 3000,
    // allowUpgrades: false,
    // upgrade: false,
    // cookie: false,
    cors: {
      origin: "*",
    },
  };
  const io = new ServerIO(server, options);

  io.on("connection", (socket) => {
    const userId = socket?.handshake?.query?.userId;

    socket.on("disconnect", () => {
      addBonusOnClose(userId);
    });
  });
  return io;
};

export { webSocketConnection, connections };
