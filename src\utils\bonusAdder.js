// MongoDb Models
import { User } from "../models/User.js";
import { Card } from "../models/Card.js";

//
import moment from "moment";
import { sio } from "../index.js";
import { ObjectId } from "mongodb";

export const addBonusOnClose = async (userId, time, updateStartTime = true) => {
  try {
    const closeTime = time ? moment(time) : moment();

    const userData = await User.aggregate([
      {
        $match: { _id: new ObjectId(userId), is_deleted: false },
      },
      {
        $lookup: {
          from: User.collection.name,
          let: { user_tg_id: "$tg_id" },
          pipeline: [
            {
              $match: {
                $expr: { $in: ["$$user_tg_id", "$referrals.tg_id"] },
              },
            },
            {
              $project: {
                referred_by: "$tg_id",
              },
            },
          ],
          as: "referred_by",
        },
      },
      {
        $unwind: {
          path: "$referred_by",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          tg_id: 1,
          total_points: 1,
          referrals: 1,
          // cards_levels: 1,
          cards_data: 1,
          start_time: 1,
          close_time: 1,
          availability_status: 1,
          referred_by: "$referred_by.referred_by",
        },
      },
    ]).exec();

    if (!(userData && userData.length > 0)) {
      return null;
    }

    const { cards_data, start_time, close_time } = userData[0];

    if (!(cards_data && start_time)) {
      return null;
    }

    const startTime = moment(start_time);

    if (close_time && moment(close_time).isAfter(startTime)) {
      return null;
    }

    const mineCards = await Card.find().exec();

    if (!mineCards) {
      return null;
    }

    const hoursDifference = closeTime.diff(startTime, "hours", true); // true returns float

    let points = 0;

    if (hoursDifference) {
      for (let cardId in cards_data) {
        const card = mineCards.find((item) => item._id === cardId);
        points +=
          card.base_pph *
          ((card.profit_increase_rate ** cards_data[cardId].level - 1) /
            (card.profit_increase_rate - 1)) *
          hoursDifference;
      }
    }

    const updateData = {
      $inc: { total_points: points },
      $set: {
        close_time: closeTime.toISOString(),
        ...(updateStartTime ? { start_time: closeTime.toISOString() } : {}),
        ...(userData[0].availability_status === "online"
          ? { availability_status: "offline" }
          : {}),
      },
    };
    const result = await User.findByIdAndUpdate(userData[0]._id, updateData, {
      new: true,
    });

    if (!result) {
      return null;
    }

    emitStatusEvent({
      status: result.availability_status,
      tg_id: userData[0].tg_id,
      referred_by: userData[0].referred_by,
      referrals: userData[0].referrals,
    });
  } catch (err) {
    return null;
  }
};

export const addBonusOnOpen = async (user, time) => {
  try {
    const startTime = time ? moment(time) : moment();
    const { cards_data, close_time } = user;

    if (!close_time) {
      return null;
    }

    const mineCards = await Card.find().exec();

    if (!mineCards) {
      return null;
    }

    const closeTime = moment(close_time);
    const hoursDifference = startTime.diff(closeTime, "hours", true); // true returns float

    let points = 0;

    if (hoursDifference) {
      for (let cardId in cards_data) {
        const card = mineCards.find((item) => item._id === cardId);
        points +=
          card.base_pph *
          ((card.profit_increase_rate ** cards_data[cardId].level - 1) /
            (card.profit_increase_rate - 1)) *
          (hoursDifference < 3 ? hoursDifference : 3);
      }
    }

    const updateData = {
      $inc: { total_points: points },
      $set: {
        start_time: startTime.toISOString(),
        ...(user.availability_status === "offline"
          ? { availability_status: "online" }
          : {}),
      },
    };
    const result = await User.findByIdAndUpdate(user._id, updateData, {
      new: true,
    });

    if (!result) {
      return null;
    }

    emitStatusEvent({
      status: result.availability_status,
      tg_id: user.tg_id,
      referred_by: user.referred_by,
      referrals: user.referrals,
    });

    return points;
  } catch (err) {
    return null;
  }
};

const emitStatusEvent = (user) => {
  if (!sio) return; // Prevent errors if `sio` is undefined

  if (user.referred_by) {
    sio.emit(`${user.referred_by}_referralStatus`, {
      referral_id: user.tg_id,
      status: user.status,
    });
  }

  if (user.referrals?.length > 0) {
    sio.emit(`${user.tg_id}_referredByStatus`, {
      status: user.status,
    });
  }
};

export const bonusPointsCalculator = async (user, time) => {
  try {
    const buyTime = moment(time);
    const { cards_data, start_time } = user;

    let points = 0;

    if (start_time) {
      const mineCards = await Card.find().exec();

      if (!mineCards) {
        return null;
      }

      const startTime = moment(start_time);
      const hoursDifference = buyTime.diff(startTime, "hours", true); // true returns float

      if (hoursDifference) {
        for (let cardId in cards_data) {
          const card = mineCards.find((item) => item._id === cardId);
          points +=
            card.base_pph *
            ((card.profit_increase_rate ** cards_data[cardId].level - 1) /
              (card.profit_increase_rate - 1)) *
            hoursDifference;
        }
      }
    }

    return { points, buyTime };
  } catch (err) {
    return null;
  }
};
