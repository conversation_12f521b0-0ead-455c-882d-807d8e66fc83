import mongoose from "mongoose";

const earnSchema = new mongoose.Schema({
  user_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  daily_reward: {
    day: { type: Number, required: true, default: 0 },
    time: { type: Date, required: true },
  },
  tg_channel: {
    value: { type: Boolean, required: true, default: false },
    check_time: { type: Date },
  },
  twitter: {
    value: { type: Boolean, required: true, default: false },
    check_time: { type: Date },
  },
  exchange_select: {
    type: Boolean,
    required: true,
    default: false,
  },
  country_select: {
    type: Boolean,
    required: true,
    default: false,
  },
  invite_three_friends: {
    type: Boolean,
    required: true,
    default: false,
  },
});

export const Earn = mongoose.model("Earn", earnSchema);
