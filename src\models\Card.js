import mongoose from "mongoose";

const cardSchema = new mongoose.Schema(
  {
    _id: { type: String, required: true },
    deck: {
      type: String,
      enum: ["company", "blockchain", "psi_book", "news", "special"],
      required: true,
    },
    name: { type: String, required: true },
    description: { type: String },
    base_price: { type: Number, required: true },
    base_pph: { type: Number, required: true },
    cost_increase_rate: { type: Number, required: true },
    profit_increase_rate: { type: Number, required: true },
    max_level: { type: Number },
    cool_down_period: { type: Number },
    card_availability: { type: Date },
    affiliated_link: {
      url: { type: String },
      text: { type: String },
    },
    image: {
      src: { type: String },
      alt: { type: String },
    },
    locked_state: {
      friends_value: { type: Number },
      admin_text: { type: String },
      message: { type: String },
    },
    shareable: {
      type: Boolean,
      default: false,
    },
    is_deleted: {
      type: Boolean,
      required: true,
      default: false,
    },
  },
  { _id: false }
);

export const Card = mongoose.model("Card", cardSchema);
