// MongoDb Models
import { User } from "../models/User.js";

export const exchangesData = async () => {
  try {
    const usersWithExchangesData = await User.aggregate([
      {
        $facet: {
          binanceUsers: [
            { $match: { exchange: "binance" } },
            { $count: "binanceCount" },
          ],
          okxUsers: [{ $match: { exchange: "okx" } }, { $count: "okxCount" }],
          cryptoComUsers: [
            { $match: { exchange: "crypto.com" } },
            { $count: "cryptoComCount" },
          ],
          bybitUsers: [
            { $match: { exchange: "bybit" } },
            { $count: "bybitCount" },
          ],
          bingXUsers: [
            { $match: { exchange: "bingX" } },
            { $count: "bingXCount" },
          ],
          htxUsers: [{ $match: { exchange: "htx" } }, { $count: "htxCount" }],
          kucoinUsers: [
            { $match: { exchange: "kucoin" } },
            { $count: "kucoinCount" },
          ],
          gateioUsers: [
            { $match: { exchange: "gate.io" } },
            { $count: "gateioCount" },
          ],
          mexcUsers: [
            { $match: { exchange: "mexc" } },
            { $count: "mexcCount" },
          ],
          bitgetUsers: [
            { $match: { exchange: "bitget" } },
            { $count: "bitgetCount" },
          ],
          notSelectedUsers: [
            {
              $match: { exchange: { $exists: false } },
            },
            { $count: "notSelectedCount" },
          ],
        },
      },
      {
        $project: {
          exchangesData: {
            $concatArrays: [
              [
                {
                  id: "binance",
                  label: "Binance",
                  value: {
                    $ifNull: [
                      { $arrayElemAt: ["$binanceUsers.binanceCount", 0] },
                      0,
                    ],
                  },
                },
                {
                  id: "okx",
                  label: "OKX",
                  value: {
                    $ifNull: [{ $arrayElemAt: ["$okxUsers.okxCount", 0] }, 0],
                  },
                },
                {
                  id: "crypto.com",
                  label: "Crypto.com",
                  value: {
                    $ifNull: [
                      { $arrayElemAt: ["$cryptoComUsers.cryptoComCount", 0] },
                      0,
                    ],
                  },
                },
                {
                  id: "bybit",
                  label: "Bybit",
                  value: {
                    $ifNull: [
                      { $arrayElemAt: ["$bybitUsers.bybitCount", 0] },
                      0,
                    ],
                  },
                },
                {
                  id: "bingX",
                  label: "BingX",
                  value: {
                    $ifNull: [
                      { $arrayElemAt: ["$bingXUsers.bingXCount", 0] },
                      0,
                    ],
                  },
                },
                {
                  id: "htx",
                  label: "HTX",
                  value: {
                    $ifNull: [{ $arrayElemAt: ["$htxUsers.htxCount", 0] }, 0],
                  },
                },
                {
                  id: "kucoin",
                  label: "Kucoin",
                  value: {
                    $ifNull: [
                      { $arrayElemAt: ["$kucoinUsers.kucoinCount", 0] },
                      0,
                    ],
                  },
                },
                {
                  id: "gate.io",
                  label: "Gate.io",
                  value: {
                    $ifNull: [
                      { $arrayElemAt: ["$gateioUsers.gateioCount", 0] },
                      0,
                    ],
                  },
                },
                {
                  id: "mexc",
                  label: "MEXC",
                  value: {
                    $ifNull: [{ $arrayElemAt: ["$mexcUsers.mexcCount", 0] }, 0],
                  },
                },
                {
                  id: "bitget",
                  label: "Bitget",
                  value: {
                    $ifNull: [
                      { $arrayElemAt: ["$bitgetUsers.bitgetCount", 0] },
                      0,
                    ],
                  },
                },
                {
                  id: "not-selected",
                  label: "Not Selected",
                  value: {
                    $ifNull: [
                      {
                        $arrayElemAt: ["$notSelectedUsers.notSelectedCount", 0],
                      },
                      0,
                    ],
                  },
                },
              ],
            ],
          },
        },
      },
      { $unwind: "$exchangesData" },
      {
        $match: {
          "exchangesData.value": { $gt: 0 },
        },
      },
      { $replaceRoot: { newRoot: "$exchangesData" } },
    ]);

    if (!usersWithExchangesData) {
      return null;
    }

    return usersWithExchangesData;
  } catch (err) {
    return null;
  }
};

export const blockchainsData = async () => {
  try {
    // List of IDs
    const ids = [
      "TON",
      "TRON",
      "Base",
      "Optimism",
      "Polygon",
      "Avalanche",
      "Solana",
      "BSC",
    ];

    // Generate the facets
    const facets = ids.reduce((acc, id) => {
      acc[id.toLowerCase()] = [
        {
          $match: {
            [`cards_data.${id}.level`]: { $exists: true },
          },
        },
        {
          $group: {
            _id: `$cards_data.${id}.level`,
            [id]: { $sum: 1 },
          },
        },
        { $sort: { _id: 1 } },
        { $project: { _id: 0, level: "$_id", [id]: 1 } },
      ];
      return acc;
    }, {});

    const data = await User.aggregate([
      { $facet: facets },
      {
        $project: {
          combined: {
            $concatArrays: Object.keys(facets).map((key) => `$${key}`),
          },
        },
      },
      { $unwind: "$combined" },
      {
        $group: {
          _id: "$combined.level",
          level: { $first: "$combined.level" },
          ...ids.reduce((acc, id) => {
            acc[id] = { $sum: { $ifNull: [`$combined.${id}`, 0] } };
            return acc;
          }, {}),
        },
      },
      {
        $project: {
          _id: 0,
          // level: 1,
          // ...ids.reduce((acc, id) => {
          //   acc[id] = 1;
          //   return acc;
          // }, {}),
        },
      },
      { $sort: { level: 1 } },
    ]);

    if (!data) {
      return null;
    }

    return { rows: data, columns: ids };
  } catch (err) {
    return null;
  }
};
