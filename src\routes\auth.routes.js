import express from "express";
import { validateBody } from "../middlewares/validateBody.js";
import {
  adminLoginSchema,
  loginSchema,
  signupSchema,
} from "../schemaValidation/auth.schema.js";
import {
  login,
  signup,
  loginWithAuth,
  deleteUser,
  adminLogin,
  adminLoginWithAuth,
} from "../controllers/auth.controllers.js";
import { adminAuth, userAuth } from "../middlewares/authentication.js";

const router = express.Router();

router.post("/login", validateBody(loginSchema), login);
router.post("/login-with-auth", userAuth, loginWithAuth);
router.post("/sign-up", validateBody(signupSchema), signup);
router.delete("/user-delete", userAuth, deleteUser);
router.post("/admin-login", validateBody(adminLoginSchema), adminLogin);
router.post("/admin-login-with-auth", adminAuth, adminLoginWithAuth);

export default router;
