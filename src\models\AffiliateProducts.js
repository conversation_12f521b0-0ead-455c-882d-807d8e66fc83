import mongoose from "mongoose";

const affiliatedCardSchema = new mongoose.Schema(
  {
    _id: { type: String, required: true },
    name: { type: String, required: true },
    availability: { type: Date, required: true },
    affiliated_link: { type: String, required: true },
    image: {
      src: { type: String, required: true },
      alt: { type: String },
    },
    total_clicks: { type: Number },
    is_deleted: {
      type: Boolean,
      required: true,
      default: false,
    },
  },
  { _id: false }
);

export const AffiliatedProduct = mongoose.model("AffiliatedProduct", affiliatedCardSchema);
