import mongoose from "mongoose";

const messageSchema = new mongoose.Schema({
  sender: {
    type: Number,
    required: true,
  },
  content: { type: String, required: true },
  status: {
    type: String,
    enum: ["sent", "read"],
    default: "sent",
  },
  created_at: { type: Date, required: true },
});

const chatSchema = new mongoose.Schema({
  room_id: { type: String, required: true, unique: true },
  messages: {
    type: [messageSchema],
    validate: (v) => Array.isArray(v) && v.length > 0,
  },
});

export const Chat = mongoose.model("Chat", chatSchema);
