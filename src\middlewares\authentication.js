// Libraries
import jwt from "jsonwebtoken";
import { Token } from "../models/Token.js";

export const userAuth = async (req, res, next) => {
  try {
    const { authorization } = req.headers;
    if (!authorization) {
      return res.status(403).json({
        message: "Authorization header is missing",
        success: false,
      });
    }

    const token = authorization.split(" ")[1];
    if (!token || token === "undefined" || token === "null") {
      return res.status(403).json({
        message: "Token is missing",
        success: false,
      });
    }

    const tokenData = await Token.findOne({ token });

    if (!tokenData) {
      return res.status(404).json({
        message: "Token doesn't exist",
        success: false,
      });
    }

    try {
      const user = jwt.verify(token, process.env.USER_TOKEN_SECRET);
      req.user = { ...user, oldToken: token };
      next();
    } catch (err) {
      // If the token is invalid or expired, delete it from the database
      await Token.deleteOne({ token });
      return res.status(403).json({
        message: "Token expired or invalid",
        success: false,
      });
    }
  } catch (error) {
    return res.status(403).json({
      message: "Authentication failed",
      success: false,
    });
  }
};

export const adminAuth = async (req, res, next) => {
  try {
    const { authorization } = req.headers;
    if (!authorization) {
      return res.status(403).json({
        message: "Authorization header is missing",
        success: false,
      });
    }

    const token = authorization.split(" ")[1];
    if (!token || token === "undefined" || token === "null") {
      return res.status(403).json({
        message: "Token is missing",
        success: false,
      });
    }

    const tokenData = await Token.findOne({ token });

    if (!tokenData) {
      return res.status(404).json({
        message: "Token doesn't exist",
        success: false,
      });
    }

    try {
      const user = jwt.verify(token, process.env.ADMIN_TOKEN_SECRET);
      if (!user.isAdmin) {
        return res.status(401).json({
          message: "Unauthorized",
          success: false,
        });
      }
      req.user = { ...user, oldToken: token };
      next();
    } catch (err) {
      // If the token is invalid or expired, delete it from the database
      await Token.deleteOne({ token });
      return res.status(403).json({
        message: "Token expired or invalid",
        success: false,
      });
    }
  } catch (error) {
    return res.status(403).json({
      message: "Authentication failed",
      success: false,
    });
  }
};

// Middleware to validate API key
export const apiKeyAuth = (req, res, next) => {
  // Get API key from headers
  const apiKey = req.headers['x-api-key'];

  // Check if API key is present
  if (!apiKey) {
    return res.status(401).json({
      error: 'API key is missing in the request headers'
    });
  }

  // Here you would typically validate the API key against your database
  // This is a simple example - replace with your actual validation logic
  const isValidApiKey = validateApiKey(apiKey);

  if (!isValidApiKey) {
    return res.status(403).json({
      error: 'Invalid API key'
    });
  }

  // If validation passes, proceed to the next middleware/route handler
  next();
};

// Example validation function (replace with your own implementation)
const validateApiKey = (apiKey) => {
  const validApiKeys = [
    // Store your valid API keys here or in a database
    process.env.WEB_API_KEY,
  ];

  return validApiKeys.includes(apiKey);
};