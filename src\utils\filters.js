export const parseFiltersFromQuery = (queryFilters) => {
  const filters = [];

  if (!queryFilters) return filters;

  for (const key in queryFilters) {
    const raw = queryFilters[key];
    const [operator, ...rest] = raw.split(":");
    const value = rest.join(":"); // handles colons in value
    const cleanOperator = operator?.trim();

    filters.push({
      key,
      operator: cleanOperator,
      value:
        cleanOperator === "isEmpty" || cleanOperator === "isNotEmpty"
          ? null
          : cleanOperator === "isAnyOf"
          ? value.split(",")
          : isNaN(value)
          ? value.trim()
          : Number(value),
    });
  }

  return filters;
};

export const buildMatchFromFilters = (filters = []) => {
  const match = { is_deleted: false };

  filters.forEach(({ key, operator, value }) => {
    if (!["tg_id", "first_name", "last_name", "username"].includes(key)) return;

    switch (operator) {
      case "equals":
        match[key] = value;
        break;
      case "contains":
        match[key] = { $regex: value, $options: "i" };
        break;
      case "startsWith":
        match[key] = { $regex: `^${value}`, $options: "i" };
        break;
      case "endsWith":
        match[key] = { $regex: `${value}$`, $options: "i" };
        break;
      case "isEmpty":
        match[key] = { $in: [null, "", undefined] };
        break;
      case "isNotEmpty":
        match[key] = { $nin: [null, "", undefined] };
        break;
      case "isAnyOf":
        if (Array.isArray(value)) {
          match[key] = { $in: value };
        }
        break;
    }
  });

  return match;
};
