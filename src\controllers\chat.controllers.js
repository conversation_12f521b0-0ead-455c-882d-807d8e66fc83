// MongoDb Models
import { sio } from "../index.js";
import { Chat } from "../models/Chat.js";
import { User } from "../models/User.js";

// utils
import { generateController } from "../utils/generateController.js";

const sendMessage = generateController(
  async (request, response, raiseException) => {
    const { userId } = request.user;
    const { roomId, receiver, message, createdAt } = request.body;

    const sender = await User.findById(userId).exec();

    let chat = await Chat.findOne({ room_id: roomId }).exec();
    if (chat) {
      chat.messages.push({
        sender: sender.tg_id,
        content: message,
        created_at: createdAt,
      });
    } else {
      chat = new Chat({
        room_id: roomId,
        messages: [
          {
            sender: sender.tg_id,
            content: message,
            created_at: createdAt,
          },
        ],
      });
    }

    await chat.save();

    if (!chat) {
      return raiseException(500, "An error occurred while sending message");
    }

    sio.emit(`${receiver}_receiveMessage`, {
      room_id: roomId,
      message: chat.messages[chat.messages.length - 1],
    });

    return {
      message: "Message sent successfully",
    };
  }
);

const readMessages = generateController(
  async (request, response, raiseException) => {
    const { userId } = request.user;
    const { room_id } = request.body;
    const roomIdInList = room_id.split("_");

    const receiver = await User.findById(userId).exec();
    const serderId =
      roomIdInList[roomIdInList[0] !== receiver.tg_id.toString() ? 0 : 1];

    const chat = await Chat.findOneAndUpdate(
      { room_id },
      { $set: { "messages.$[elem].status": "read" } }, // Update only filtered messages
      {
        arrayFilters: [{ "elem.sender": serderId }], // Match messages with sender 123131333
        new: true, // Return updated document
      }
    );

    if (!chat) {
      return raiseException(500, "An error occurred while reading messages");
    }

    const unreadMessages = chat.messages.filter(
      (msg) => msg.sender === serderId && msg.status !== "read"
    );

    if (unreadMessages.length > 0) {
      return raiseException(500, "Some messages were not updated to read");
    }

    sio.emit(`${serderId}_readMessages`, {
      room_id,
      receiver_id: receiver.tg_id,
    });

    return {
      message: "Message read successfully",
    };
  }
);

export { sendMessage, readMessages };
