import mongoose from "mongoose";

const boosterSchema = new mongoose.Schema({
  user_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  energy_level: {
    type: Number,
    required: true,
    default: 0,
  },
  energy: {
    type: Number,
    required: true,
    default: 1500,
  },
  energy_time: {
    type: Date,
    required: true,
  },
  multitap_level: {
    type: Number,
    required: true,
    default: 0,
  },
});

export const Booster = mongoose.model("Booster", boosterSchema);
