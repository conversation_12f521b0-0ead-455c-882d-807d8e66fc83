import joi from "joi";

const updatePointsSchema = joi.object().keys({
  taps: joi.number().required(),
  energy: joi.number().required(),
});

const updateWalletAddressSchema = joi.object().keys({
  wallet_address: joi
    .string()
    .required()
    .custom((value, helper) => {
      if (!value.match(/^0x[a-fA-F0-9]{40}$/g)) {
        return helper.message(
          "`wallet_address` must be valid ethereum address"
        );
      } else {
        return true;
      }
    }),
});

const updateInGameNameSchema = joi.object().keys({
  in_game_name: joi.required(),
});

const updateExchangeSchema = joi.object().keys({
  exchange: joi
    .string()
    .valid(
      "binance",
      "okx",
      "crypto.com",
      "bybit",
      "bingX",
      "htx",
      "kucoin",
      "gate.io",
      "mexc",
      "bitget"
    )
    .required(),
});

const updateCountrySchema = joi.object().keys({
  country: joi.string().required(),
});

const cardPurchaseSchema = joi.object().keys({
  card_id: joi.string().required(),
  buy_time: joi.string().isoDate().required(),
});

const affiliateProductInteractionSchema = joi.object().keys({
  product_id: joi.string().required()
});

const socialCardInteractionSchema = joi.object().keys({
  card_id: joi.string().required()
});

const boosterPurchaseSchema = joi.object().keys({
  type: joi.string().valid("energy", "multitap").required(),
  buy_time: joi.string().isoDate().required(),
});

const socialPointsPurchaseSchema = joi.object().keys({
  social_points_bought: joi.number().required(),
  transactionHash: joi.string().required(),
  userReferee: joi.number().required(),
});

const changeSettingsSchema = joi.object().keys({
  key: joi.string().valid("haptic_feedback", "language").required(),
  value: joi.any().required(),
});

const closeAppSchema = joi.object().keys({
  time: joi.string().isoDate().required(),
});

const continueAppSchema = joi.object().keys({
  time: joi.string().isoDate().required(),
});

export {
  updatePointsSchema,
  updateWalletAddressSchema,
  updateInGameNameSchema,
  updateExchangeSchema,
  updateCountrySchema,
  cardPurchaseSchema,
  socialPointsPurchaseSchema,
  affiliateProductInteractionSchema,
  socialCardInteractionSchema,
  boosterPurchaseSchema,
  changeSettingsSchema,
  closeAppSchema,
  continueAppSchema,
};
